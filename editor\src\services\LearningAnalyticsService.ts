import { ApiService } from '../types/missing-modules';

/**
 * 学习分析服务
 * 提供学习数据分析和推荐相关的API调用
 */
export class LearningAnalyticsService {
  private apiService: ApiService;

  constructor() {
    this.apiService = new ApiService();
  }

  /**
   * 获取用户学习画像
   * @param userId 用户ID
   * @param includeAnalysis 是否包含分析结果
   * @returns 用户画像数据
   */
  async getLearnerProfile(userId: string, includeAnalysis: boolean = true): Promise<any> {
    try {
      const response = await this.apiService.get(`/learning-tracking/profile/${userId}`, {
        params: { includeAnalysis }
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || '获取用户画像失败');
      }
    } catch (error: any) {
      console.error('获取用户画像失败:', error);
      throw new Error(error.response?.data?.message || error.message || '获取用户画像失败');
    }
  }

  /**
   * 获取个性化推荐
   * @param userId 用户ID
   * @param options 推荐选项
   * @returns 推荐列表
   */
  async getRecommendations(userId: string, options: {
    limit?: number;
    types?: string[];
    knowledgeAreas?: string[];
    difficulty?: string;
  } = {}): Promise<any[]> {
    try {
      const params: any = {};
      
      if (options.limit) params.limit = options.limit;
      if (options.types) params.types = options.types.join(',');
      if (options.knowledgeAreas) params.knowledgeAreas = options.knowledgeAreas.join(',');
      if (options.difficulty) params.difficulty = options.difficulty;

      const response = await this.apiService.get(`/learning-tracking/recommendations/${userId}`, {
        params
      });
      
      if (response.success) {
        return response.data.recommendations || [];
      } else {
        throw new Error(response.message || '获取推荐失败');
      }
    } catch (error: any) {
      console.error('获取推荐失败:', error);
      throw new Error(error.response?.data?.message || error.message || '获取推荐失败');
    }
  }

  /**
   * 更新推荐反馈
   * @param recommendationId 推荐ID
   * @param feedback 反馈信息
   */
  async updateRecommendationFeedback(
    recommendationId: string, 
    feedback: {
      action: 'accepted' | 'rejected' | 'ignored' | 'completed';
      rating?: number;
      comment?: string;
      timeToDecision?: number;
    }
  ): Promise<void> {
    try {
      const response = await this.apiService.put(
        `/learning-tracking/recommendations/${recommendationId}/feedback`,
        feedback
      );
      
      if (!response.success) {
        throw new Error(response.message || '更新推荐反馈失败');
      }
    } catch (error: any) {
      console.error('更新推荐反馈失败:', error);
      throw new Error(error.response?.data?.message || error.message || '更新推荐反馈失败');
    }
  }

  /**
   * 获取学习分析报告
   * @param userId 用户ID
   * @param timeRange 时间范围
   * @returns 学习分析报告
   */
  async getLearningAnalytics(userId: string, timeRange?: {
    startDate: string;
    endDate: string;
  }): Promise<any> {
    try {
      const params: any = {};
      
      if (timeRange) {
        params.startDate = timeRange.startDate;
        params.endDate = timeRange.endDate;
      }

      const response = await this.apiService.get(`/learning-tracking/analytics/${userId}`, {
        params
      });
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || '获取学习分析报告失败');
      }
    } catch (error: any) {
      console.error('获取学习分析报告失败:', error);
      throw new Error(error.response?.data?.message || error.message || '获取学习分析报告失败');
    }
  }

  /**
   * 获取推荐历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 推荐历史
   */
  async getRecommendationHistory(userId: string, limit: number = 20): Promise<any[]> {
    try {
      const response = await this.apiService.get(`/learning-tracking/recommendations/${userId}/history`, {
        params: { limit }
      });
      
      if (response.success) {
        return response.data.recommendations || [];
      } else {
        throw new Error(response.message || '获取推荐历史失败');
      }
    } catch (error: any) {
      console.error('获取推荐历史失败:', error);
      throw new Error(error.response?.data?.message || error.message || '获取推荐历史失败');
    }
  }

  /**
   * 获取同步状态
   * @returns 同步状态信息
   */
  async getSyncStatus(): Promise<any> {
    try {
      const response = await this.apiService.get('/learning-tracking/sync/status');
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || '获取同步状态失败');
      }
    } catch (error: any) {
      console.error('获取同步状态失败:', error);
      throw new Error(error.response?.data?.message || error.message || '获取同步状态失败');
    }
  }

  /**
   * 手动触发同步
   */
  async triggerSync(): Promise<void> {
    try {
      const response = await this.apiService.post('/learning-tracking/sync/trigger');
      
      if (!response.success) {
        throw new Error(response.message || '触发同步失败');
      }
    } catch (error: any) {
      console.error('触发同步失败:', error);
      throw new Error(error.response?.data?.message || error.message || '触发同步失败');
    }
  }

  /**
   * 发送学习记录
   * @param statements xAPI语句数组
   */
  async sendLearningRecords(statements: any[]): Promise<void> {
    try {
      const response = await this.apiService.post('/learning-tracking/statements/batch', {
        statements
      });
      
      if (!response.success) {
        throw new Error(response.message || '发送学习记录失败');
      }
    } catch (error: any) {
      console.error('发送学习记录失败:', error);
      throw new Error(error.response?.data?.message || error.message || '发送学习记录失败');
    }
  }

  /**
   * 获取学习统计摘要
   * @param userId 用户ID
   * @returns 统计摘要
   */
  async getLearningStatsSummary(userId: string): Promise<{
    totalActivities: number;
    activeDays: number;
    knowledgeAreasCount: number;
    averageScore: number;
    completedActivities: number;
    lastActivityTime: string;
  }> {
    try {
      const analytics = await this.getLearningAnalytics(userId);
      const profile = analytics.profile;
      const progressSummary = analytics.progressSummary;

      return {
        totalActivities: analytics.recommendationStats?.totalRecommendations || 0,
        activeDays: 0, // 需要从后端计算
        knowledgeAreasCount: progressSummary?.totalKnowledgeAreas || 0,
        averageScore: progressSummary?.averageConfidence || 0,
        completedActivities: progressSummary?.masteredAreas || 0,
        lastActivityTime: profile?.updatedAt || new Date().toISOString()
      };
    } catch (error: any) {
      console.error('获取学习统计摘要失败:', error);
      throw new Error('获取学习统计摘要失败');
    }
  }

  /**
   * 获取知识领域进度
   * @param userId 用户ID
   * @returns 知识领域进度
   */
  async getKnowledgeAreaProgress(userId: string): Promise<Array<{
    area: string;
    confidence: number;
    level: string;
    timeSpent: number;
    weakPoints: string[];
    strengths: string[];
  }>> {
    try {
      const profileData = await this.getLearnerProfile(userId);
      const knowledgeAreas = profileData.profile?.knowledgeAreas || {};

      return Object.entries(knowledgeAreas).map(([area, knowledge]: [string, any]) => ({
        area,
        confidence: knowledge.confidence || 0,
        level: knowledge.level || 'beginner',
        timeSpent: knowledge.timeSpent || 0,
        weakPoints: knowledge.weakPoints || [],
        strengths: knowledge.strengths || []
      }));
    } catch (error: any) {
      console.error('获取知识领域进度失败:', error);
      throw new Error('获取知识领域进度失败');
    }
  }

  /**
   * 获取情感状态分析
   * @param userId 用户ID
   * @returns 情感状态分析
   */
  async getEmotionalAnalysis(userId: string): Promise<{
    dominantEmotions: string[];
    emotionalStability: number;
    motivationLevel: number;
    confidenceLevel: number;
  }> {
    try {
      const profileData = await this.getLearnerProfile(userId);
      const emotionalTraits = profileData.profile?.emotionalTraits || {};

      return {
        dominantEmotions: emotionalTraits.dominantEmotions || [],
        emotionalStability: emotionalTraits.emotionalStability || 0.5,
        motivationLevel: emotionalTraits.motivationLevel || 0.5,
        confidenceLevel: emotionalTraits.confidenceLevel || 0.5
      };
    } catch (error: any) {
      console.error('获取情感状态分析失败:', error);
      throw new Error('获取情感状态分析失败');
    }
  }

  /**
   * 获取学习建议
   * @param userId 用户ID
   * @returns 个性化学习建议
   */
  async getLearningInsights(userId: string): Promise<string[]> {
    try {
      const analytics = await this.getLearningAnalytics(userId);
      return analytics.insights || [];
    } catch (error: any) {
      console.error('获取学习建议失败:', error);
      throw new Error('获取学习建议失败');
    }
  }
}
