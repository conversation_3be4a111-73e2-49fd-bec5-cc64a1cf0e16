/**
 * AI服务类
 * 负责与AI后端服务的通信和数据处理
 */
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { UserIntent, IntentType, AIResponse } from '../hooks/useAIAssistant';
import { ChatMessage } from '../components/ai/AIChatPanel';

// AI服务配置
export interface AIServiceConfig {
  apiEndpoint: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
  retryAttempts: number;
  apiKey?: string;
}

// 反馈数据
export interface FeedbackData {
  messageId: string;
  feedback: 'positive' | 'negative';
  comment?: string;
  timestamp: number;
}

// 请求重试配置
interface RetryConfig {
  attempts: number;
  delay: number;
  backoff: number;
}

/**
 * AI服务类
 */
export class AIService {
  protected config: AIServiceConfig;
  protected httpClient: AxiosInstance;
  private retryConfig: RetryConfig;
  private requestQueue: Map<string, Promise<any>> = new Map();

  constructor(config: AIServiceConfig) {
    this.config = config;
    this.retryConfig = {
      attempts: config.retryAttempts,
      delay: 1000,
      backoff: 2
    };

    // 创建HTTP客户端
    this.httpClient = axios.create({
      baseURL: config.apiEndpoint,
      timeout: config.timeout,
      headers: {
        'Content-Type': 'application/json',
        ...(config.apiKey && { 'Authorization': `Bearer ${config.apiKey}` })
      }
    });

    // 设置请求拦截器
    this.setupRequestInterceptors();
    
    // 设置响应拦截器
    this.setupResponseInterceptors();
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    try {
      // 测试连接
      await this.httpClient.get('/health');
      console.log('AI服务连接成功');
    } catch (error) {
      console.error('AI服务连接失败:', error);
      throw new Error('无法连接到AI服务');
    }
  }

  /**
   * 分析用户意图
   */
  async analyzeIntent(input: string): Promise<UserIntent> {
    const requestId = this.generateRequestId();
    
    // 检查是否有相同的请求正在处理
    if (this.requestQueue.has(input)) {
      return this.requestQueue.get(input)!;
    }

    const requestPromise = this.executeWithRetry(async () => {
      const response = await this.httpClient.post('/intent/analyze', {
        text: input,
        model: this.config.model,
        requestId
      });

      return this.parseIntentResponse(response.data);
    });

    this.requestQueue.set(input, requestPromise);

    try {
      const result = await requestPromise;
      return result;
    } finally {
      this.requestQueue.delete(input);
    }
  }

  /**
   * 生成AI响应
   */
  async generateResponse(
    intent: UserIntent,
    context: {
      context: any;
      history: ChatMessage[];
      userId: string;
    }
  ): Promise<AIResponse> {
    const requestId = this.generateRequestId();

    return this.executeWithRetry(async () => {
      const response = await this.httpClient.post('/chat/generate', {
        intent,
        context: context.context,
        history: this.formatChatHistory(context.history),
        userId: context.userId,
        model: this.config.model,
        maxTokens: this.config.maxTokens,
        temperature: this.config.temperature,
        requestId
      });

      return this.parseGenerationResponse(response.data);
    });
  }

  /**
   * 提交用户反馈
   */
  async submitFeedback(feedback: FeedbackData): Promise<void> {
    return this.executeWithRetry(async () => {
      await this.httpClient.post('/feedback', feedback);
    });
  }

  /**
   * 获取AI能力列表
   */
  async getCapabilities(): Promise<string[]> {
    return this.executeWithRetry(async () => {
      const response = await this.httpClient.get('/capabilities');
      return response.data.capabilities || [];
    });
  }

  /**
   * 切换AI模型
   */
  async switchModel(modelName: string): Promise<void> {
    return this.executeWithRetry(async () => {
      await this.httpClient.post('/model/switch', {
        model: modelName
      });
      
      this.config.model = modelName;
    });
  }

  /**
   * 重新连接
   */
  async reconnect(): Promise<void> {
    // 清空请求队列
    this.requestQueue.clear();
    
    // 重新初始化
    await this.initialize();
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.requestQueue.clear();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptors(): void {
    this.httpClient.interceptors.request.use(
      (config) => {
        // 添加请求时间戳
        config.metadata = { startTime: Date.now() };
        
        // 添加请求ID
        if (!config.headers['X-Request-ID']) {
          config.headers['X-Request-ID'] = this.generateRequestId();
        }

        console.log(`AI请求发送: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('AI请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptors(): void {
    this.httpClient.interceptors.response.use(
      (response) => {
        // 计算请求时间
        const startTime = response.config.metadata?.startTime;
        if (startTime) {
          const duration = Date.now() - startTime;
          console.log(`AI请求完成: ${response.config.url} (${duration}ms)`);
        }

        return response;
      },
      (error) => {
        console.error('AI响应错误:', error);
        
        // 处理特定错误
        if (error.response) {
          const { status, data } = error.response;
          
          switch (status) {
            case 401:
              throw new Error('AI服务认证失败');
            case 403:
              throw new Error('AI服务访问被拒绝');
            case 429:
              throw new Error('AI服务请求频率过高，请稍后重试');
            case 500:
              throw new Error('AI服务内部错误');
            case 503:
              throw new Error('AI服务暂时不可用');
            default:
              throw new Error(data?.message || `AI服务错误 (${status})`);
          }
        } else if (error.request) {
          throw new Error('无法连接到AI服务');
        } else {
          throw new Error('AI请求配置错误');
        }
      }
    );
  }

  /**
   * 带重试的执行
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    attempt: number = 1
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (attempt < this.retryConfig.attempts && this.shouldRetry(error)) {
        const delay = this.retryConfig.delay * Math.pow(this.retryConfig.backoff, attempt - 1);
        
        console.log(`AI请求重试 ${attempt}/${this.retryConfig.attempts}，${delay}ms后重试`);
        
        await this.sleep(delay);
        return this.executeWithRetry(operation, attempt + 1);
      }
      
      throw error;
    }
  }

  /**
   * 判断是否应该重试
   */
  private shouldRetry(error: any): boolean {
    // 网络错误或5xx错误可以重试
    if (!error.response) {
      return true;
    }
    
    const status = error.response.status;
    return status >= 500 || status === 429;
  }

  /**
   * 解析意图分析响应
   */
  private parseIntentResponse(data: any): UserIntent {
    return {
      type: data.intent?.type || IntentType.UNKNOWN,
      confidence: data.intent?.confidence || 0,
      entities: data.entities || [],
      parameters: data.parameters || {},
      context: data.context || []
    };
  }

  /**
   * 解析生成响应
   */
  private parseGenerationResponse(data: any): AIResponse {
    return {
      text: data.response?.text || '',
      actions: data.response?.actions || [],
      suggestions: data.response?.suggestions || [],
      model: data.model || this.config.model,
      confidence: data.confidence || 0,
      processingTime: data.processingTime || 0,
      tokens: data.tokens || 0,
      metadata: data.metadata || {}
    };
  }

  /**
   * 格式化聊天历史
   */
  private formatChatHistory(history: ChatMessage[]): any[] {
    return history.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content,
      timestamp: msg.timestamp
    }));
  }

  /**
   * 生成请求ID
   */
  protected generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取服务状态
   */
  async getStatus(): Promise<{
    connected: boolean;
    model: string;
    version: string;
    uptime: number;
  }> {
    try {
      const response = await this.httpClient.get('/status');
      return {
        connected: true,
        model: this.config.model,
        version: response.data.version || '1.0.0',
        uptime: response.data.uptime || 0
      };
    } catch (error) {
      return {
        connected: false,
        model: this.config.model,
        version: '未知',
        uptime: 0
      };
    }
  }

  /**
   * 获取使用统计
   */
  async getUsageStats(): Promise<{
    totalRequests: number;
    totalTokens: number;
    averageResponseTime: number;
    errorRate: number;
  }> {
    try {
      const response = await this.httpClient.get('/stats');
      return response.data;
    } catch (error) {
      return {
        totalRequests: 0,
        totalTokens: 0,
        averageResponseTime: 0,
        errorRate: 0
      };
    }
  }
}
