/**
 * 缺失模块的类型声明
 */

// API服务类型声明
declare module './ApiService' {
  export interface ApiService {
    get(url: string): Promise<any>;
    post(url: string, data: any): Promise<any>;
    put(url: string, data: any): Promise<any>;
    delete(url: string): Promise<any>;
  }
  
  export const apiService: ApiService;
}

// 行为树类型声明
declare module '../types/BehaviorTree' {
  export interface BehaviorTreeConfig {
    id: string;
    name: string;
    nodes: BehaviorTreeNode[];
    connections: BehaviorTreeConnection[];
  }
  
  export interface BehaviorTreeNode {
    id: string;
    type: string;
    position: { x: number; y: number };
    data: any;
  }
  
  export interface BehaviorTreeConnection {
    id: string;
    source: string;
    target: string;
  }
}

// 用户偏好类型
export interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  autoSave: boolean;
  shortcuts: Record<string, string>;
}

// 焦点区域类型
export interface FocusArea {
  id: string;
  name: string;
  description: string;
  priority: number;
}

// 代码生成器类型
export interface CodeGenerator {
  generateCode(template: string, data: any): string;
  validateCode(code: string): boolean;
}

// 重构引擎类型
export interface RefactorEngine {
  refactorCode(code: string, options: any): string;
  analyzeCode(code: string): any;
}

// 智能建议类型
export interface IntelligentSuggestion {
  id: string;
  type: string;
  title: string;
  description: string;
  confidence: number;
  action?: () => void;
}

// 增强上下文类型
export interface EnhancedContext {
  project: any;
  user: any;
  environment: any;
  history: any[];
}
