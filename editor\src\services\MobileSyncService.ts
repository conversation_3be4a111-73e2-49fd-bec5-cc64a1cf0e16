/**
 * 移动端数据同步服务
 * 
 * 提供移动端与桌面端之间的数据同步功能，包括：
 * - 实时数据同步
 * - 离线数据缓存
 * - 冲突解决机制
 * - 增量同步优化
 */

import { EventEmitter } from 'events';
import { BehaviorTreeConfig } from '../types/missing-modules';

/**
 * 同步状态枚举
 */
export enum SyncStatus {
  IDLE = 'idle',
  SYNCING = 'syncing',
  CONFLICT = 'conflict',
  ERROR = 'error',
  OFFLINE = 'offline'
}

/**
 * 数据变更类型
 */
export enum ChangeType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  MOVE = 'move'
}

/**
 * 数据变更记录
 */
export interface DataChange {
  id: string;
  type: ChangeType;
  entityType: 'node' | 'tree' | 'config';
  entityId: string;
  data: any;
  timestamp: number;
  deviceId: string;
  userId: string;
  version: number;
}

/**
 * 同步配置
 */
export interface SyncConfig {
  serverUrl: string;
  apiKey: string;
  userId: string;
  deviceId: string;
  syncInterval: number;
  maxRetries: number;
  conflictResolution: 'client_wins' | 'server_wins' | 'manual';
  enableRealtime: boolean;
  enableOfflineMode: boolean;
  compressionEnabled: boolean;
}

/**
 * 冲突解决策略
 */
export interface ConflictResolution {
  changeId: string;
  resolution: 'accept_local' | 'accept_remote' | 'merge';
  mergedData?: any;
}

/**
 * 同步统计
 */
export interface SyncStats {
  totalChanges: number;
  pendingChanges: number;
  conflictCount: number;
  lastSyncTime: number;
  syncDuration: number;
  dataTransferred: number;
  errorCount: number;
}

/**
 * 移动端数据同步服务
 */
export class MobileSyncService extends EventEmitter {
  private config: SyncConfig;
  private syncStatus: SyncStatus = SyncStatus.IDLE;
  private pendingChanges: DataChange[] = [];
  private conflictQueue: DataChange[] = [];
  private syncTimer?: NodeJS.Timeout;
  private websocket?: WebSocket;
  private isOnline = typeof navigator !== 'undefined' ? navigator.onLine : true;
  private lastSyncTime = 0;
  private syncStats: SyncStats = {
    totalChanges: 0,
    pendingChanges: 0,
    conflictCount: 0,
    lastSyncTime: 0,
    syncDuration: 0,
    dataTransferred: 0,
    errorCount: 0
  };

  constructor(config: SyncConfig) {
    super();
    this.config = config;
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private initializeService(): void {
    // 监听网络状态变化（仅在浏览器环境中）
    if (typeof window !== 'undefined') {
      window.addEventListener('online', this.handleOnline.bind(this));
      window.addEventListener('offline', this.handleOffline.bind(this));
    }

    // 启动定期同步
    if (this.config.syncInterval > 0) {
      this.startPeriodicSync();
    }

    // 启动实时同步
    if (this.config.enableRealtime && this.isOnline) {
      this.connectWebSocket();
    }

    // 加载本地待同步数据
    this.loadPendingChanges();
  }

  /**
   * 记录数据变更
   */
  public recordChange(
    type: ChangeType,
    entityType: 'node' | 'tree' | 'config',
    entityId: string,
    data: any
  ): void {
    const change: DataChange = {
      id: `change_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      entityType,
      entityId,
      data: this.config.compressionEnabled ? this.compressData(data) : data,
      timestamp: Date.now(),
      deviceId: this.config.deviceId,
      userId: this.config.userId,
      version: this.getNextVersion(entityId)
    };
    
    this.pendingChanges.push(change);
    this.syncStats.pendingChanges = this.pendingChanges.length;
    
    // 保存到本地存储
    this.savePendingChanges();
    
    // 触发实时同步
    if (this.config.enableRealtime && this.isOnline) {
      this.sendChangeToServer(change);
    }
    
    this.emit('changeRecorded', change);
  }

  /**
   * 手动触发同步
   */
  public async sync(): Promise<void> {
    if (this.syncStatus === SyncStatus.SYNCING) {
      return;
    }
    
    if (!this.isOnline) {
      this.setSyncStatus(SyncStatus.OFFLINE);
      return;
    }
    
    this.setSyncStatus(SyncStatus.SYNCING);
    const startTime = Date.now();
    
    try {
      // 上传本地变更
      await this.uploadChanges();
      
      // 下载远程变更
      await this.downloadChanges();
      
      // 解决冲突
      if (this.conflictQueue.length > 0) {
        await this.resolveConflicts();
      }
      
      this.lastSyncTime = Date.now();
      this.syncStats.lastSyncTime = this.lastSyncTime;
      this.syncStats.syncDuration = this.lastSyncTime - startTime;
      
      this.setSyncStatus(SyncStatus.IDLE);
      this.emit('syncCompleted', this.syncStats);
      
    } catch (error) {
      this.syncStats.errorCount++;
      this.setSyncStatus(SyncStatus.ERROR);
      this.emit('syncError', error);
      throw error;
    }
  }

  /**
   * 上传本地变更
   */
  private async uploadChanges(): Promise<void> {
    if (this.pendingChanges.length === 0) {
      return;
    }
    
    const batchSize = 50;
    const batches = this.chunkArray(this.pendingChanges, batchSize);
    
    for (const batch of batches) {
      try {
        const response = await fetch(`${this.config.serverUrl}/api/mobile/sync/upload`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.config.apiKey}`,
            'X-Device-ID': this.config.deviceId,
            'X-User-ID': this.config.userId
          },
          body: JSON.stringify({
            changes: batch,
            deviceId: this.config.deviceId,
            timestamp: Date.now()
          })
        });
        
        if (!response.ok) {
          throw new Error(`上传失败: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        // 处理服务器响应
        if (result.conflicts && result.conflicts.length > 0) {
          this.conflictQueue.push(...result.conflicts);
        }
        
        // 移除已成功上传的变更
        if (result.accepted) {
          this.pendingChanges = this.pendingChanges.filter(
            change => !result.accepted.includes(change.id)
          );
        }
        
        this.syncStats.dataTransferred += JSON.stringify(batch).length;
        
      } catch (error) {
        console.error('上传变更失败:', error);
        throw error;
      }
    }
    
    this.syncStats.pendingChanges = this.pendingChanges.length;
    this.savePendingChanges();
  }

  /**
   * 下载远程变更
   */
  private async downloadChanges(): Promise<void> {
    try {
      const response = await fetch(`${this.config.serverUrl}/api/mobile/sync/download`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'X-Device-ID': this.config.deviceId,
          'X-User-ID': this.config.userId,
          'X-Last-Sync': this.lastSyncTime.toString()
        }
      });
      
      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      if (result.changes && result.changes.length > 0) {
        await this.applyRemoteChanges(result.changes);
        this.syncStats.dataTransferred += JSON.stringify(result.changes).length;
      }
      
    } catch (error) {
      console.error('下载变更失败:', error);
      throw error;
    }
  }

  /**
   * 应用远程变更
   */
  private async applyRemoteChanges(changes: DataChange[]): Promise<void> {
    for (const change of changes) {
      try {
        // 检查是否有本地冲突
        const localConflict = this.pendingChanges.find(
          local => local.entityId === change.entityId && 
                   local.timestamp > change.timestamp
        );
        
        if (localConflict) {
          // 添加到冲突队列
          this.conflictQueue.push(change);
          continue;
        }
        
        // 应用变更
        await this.applyChange(change);
        this.syncStats.totalChanges++;
        
      } catch (error) {
        console.error('应用远程变更失败:', error);
      }
    }
  }

  /**
   * 应用单个变更
   */
  private async applyChange(change: DataChange): Promise<void> {
    const data = this.config.compressionEnabled ? 
      this.decompressData(change.data) : change.data;
    
    switch (change.type) {
      case ChangeType.CREATE:
        this.emit('remoteCreate', {
          entityType: change.entityType,
          entityId: change.entityId,
          data
        });
        break;
        
      case ChangeType.UPDATE:
        this.emit('remoteUpdate', {
          entityType: change.entityType,
          entityId: change.entityId,
          data
        });
        break;
        
      case ChangeType.DELETE:
        this.emit('remoteDelete', {
          entityType: change.entityType,
          entityId: change.entityId
        });
        break;
        
      case ChangeType.MOVE:
        this.emit('remoteMove', {
          entityType: change.entityType,
          entityId: change.entityId,
          data
        });
        break;
    }
  }

  /**
   * 解决冲突
   */
  private async resolveConflicts(): Promise<void> {
    if (this.conflictQueue.length === 0) {
      return;
    }
    
    this.setSyncStatus(SyncStatus.CONFLICT);
    
    for (const conflict of this.conflictQueue) {
      const resolution = await this.getConflictResolution(conflict);
      
      switch (resolution.resolution) {
        case 'accept_local':
          // 保持本地版本，忽略远程变更
          break;
          
        case 'accept_remote':
          // 应用远程变更，丢弃本地变更
          await this.applyChange(conflict);
          this.removePendingChange(conflict.entityId);
          break;
          
        case 'merge':
          // 合并变更
          if (resolution.mergedData) {
            await this.applyChange({
              ...conflict,
              data: resolution.mergedData
            });
          }
          break;
      }
    }
    
    this.conflictQueue = [];
    this.syncStats.conflictCount += this.conflictQueue.length;
  }

  /**
   * 获取冲突解决策略
   */
  private async getConflictResolution(conflict: DataChange): Promise<ConflictResolution> {
    switch (this.config.conflictResolution) {
      case 'client_wins':
        return {
          changeId: conflict.id,
          resolution: 'accept_local'
        };
        
      case 'server_wins':
        return {
          changeId: conflict.id,
          resolution: 'accept_remote'
        };
        
      case 'manual':
        // 触发手动解决事件
        return new Promise((resolve) => {
          this.emit('conflictResolutionRequired', {
            conflict,
            resolve: (resolution: ConflictResolution) => resolve(resolution)
          });
        });
        
      default:
        return {
          changeId: conflict.id,
          resolution: 'accept_remote'
        };
    }
  }

  /**
   * 连接WebSocket进行实时同步
   */
  private connectWebSocket(): void {
    if (this.websocket) {
      this.websocket.close();
    }
    
    const wsUrl = this.config.serverUrl.replace(/^http/, 'ws') + 
      `/ws/mobile/sync?token=${this.config.apiKey}&deviceId=${this.config.deviceId}`;
    
    this.websocket = new WebSocket(wsUrl);
    
    this.websocket.onopen = () => {
      console.log('WebSocket连接已建立');
      this.emit('realtimeConnected');
    };
    
    this.websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleRealtimeMessage(message);
      } catch (error) {
        console.error('处理WebSocket消息失败:', error);
      }
    };
    
    this.websocket.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.emit('realtimeDisconnected');
      
      // 重连
      if (this.isOnline && this.config.enableRealtime) {
        setTimeout(() => this.connectWebSocket(), 5000);
      }
    };
    
    this.websocket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.emit('realtimeError', error);
    };
  }

  /**
   * 处理实时消息
   */
  private handleRealtimeMessage(message: any): void {
    switch (message.type) {
      case 'change':
        this.applyChange(message.data);
        break;
        
      case 'conflict':
        this.conflictQueue.push(message.data);
        this.emit('conflictDetected', message.data);
        break;
        
      case 'sync_request':
        this.sync();
        break;
    }
  }

  /**
   * 发送变更到服务器
   */
  private sendChangeToServer(change: DataChange): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify({
        type: 'change',
        data: change
      }));
    }
  }

  /**
   * 启动定期同步
   */
  private startPeriodicSync(): void {
    this.syncTimer = setInterval(() => {
      if (this.isOnline && this.syncStatus === SyncStatus.IDLE) {
        this.sync().catch(console.error);
      }
    }, this.config.syncInterval);
  }

  /**
   * 处理网络连接
   */
  private handleOnline(): void {
    this.isOnline = true;
    this.emit('networkOnline');
    
    // 触发同步
    if (this.pendingChanges.length > 0) {
      this.sync().catch(console.error);
    }
    
    // 重连WebSocket
    if (this.config.enableRealtime) {
      this.connectWebSocket();
    }
  }

  /**
   * 处理网络断开
   */
  private handleOffline(): void {
    this.isOnline = false;
    this.setSyncStatus(SyncStatus.OFFLINE);
    this.emit('networkOffline');
    
    // 关闭WebSocket
    if (this.websocket) {
      this.websocket.close();
    }
  }

  /**
   * 设置同步状态
   */
  private setSyncStatus(status: SyncStatus): void {
    this.syncStatus = status;
    this.emit('syncStatusChanged', status);
  }

  /**
   * 获取下一个版本号
   */
  private getNextVersion(entityId: string): number {
    const existing = this.pendingChanges
      .filter(change => change.entityId === entityId)
      .map(change => change.version)
      .sort((a, b) => b - a);
    
    return existing.length > 0 ? existing[0] + 1 : 1;
  }

  /**
   * 移除待同步变更
   */
  private removePendingChange(entityId: string): void {
    this.pendingChanges = this.pendingChanges.filter(
      change => change.entityId !== entityId
    );
    this.syncStats.pendingChanges = this.pendingChanges.length;
    this.savePendingChanges();
  }

  /**
   * 保存待同步数据到本地
   */
  private savePendingChanges(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem(
          `mobile_sync_pending_${this.config.userId}`,
          JSON.stringify(this.pendingChanges)
        );
      }
    } catch (error) {
      console.error('保存待同步数据失败:', error);
    }
  }

  /**
   * 加载本地待同步数据
   */
  private loadPendingChanges(): void {
    try {
      if (typeof localStorage !== 'undefined') {
        const stored = localStorage.getItem(`mobile_sync_pending_${this.config.userId}`);
        if (stored) {
          this.pendingChanges = JSON.parse(stored);
          this.syncStats.pendingChanges = this.pendingChanges.length;
        }
      }
    } catch (error) {
      console.error('加载待同步数据失败:', error);
    }
  }

  /**
   * 数据压缩
   */
  private compressData(data: any): string {
    // 简化的压缩实现
    return JSON.stringify(data);
  }

  /**
   * 数据解压缩
   */
  private decompressData(compressedData: string): any {
    // 简化的解压缩实现
    return JSON.parse(compressedData);
  }

  /**
   * 数组分块
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 获取同步状态
   */
  public getSyncStatus(): SyncStatus {
    return this.syncStatus;
  }

  /**
   * 获取同步统计
   */
  public getSyncStats(): SyncStats {
    return { ...this.syncStats };
  }

  /**
   * 获取待同步变更数量
   */
  public getPendingChangesCount(): number {
    return this.pendingChanges.length;
  }

  /**
   * 获取冲突数量
   */
  public getConflictCount(): number {
    return this.conflictQueue.length;
  }

  /**
   * 清除所有数据
   */
  public clearAllData(): void {
    this.pendingChanges = [];
    this.conflictQueue = [];
    this.syncStats = {
      totalChanges: 0,
      pendingChanges: 0,
      conflictCount: 0,
      lastSyncTime: 0,
      syncDuration: 0,
      dataTransferred: 0,
      errorCount: 0
    };
    
    if (typeof localStorage !== 'undefined') {
      localStorage.removeItem(`mobile_sync_pending_${this.config.userId}`);
    }
    this.emit('dataCleared');
  }

  /**
   * 销毁服务
   */
  public destroy(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
    }
    
    if (this.websocket) {
      this.websocket.close();
    }
    
    if (typeof window !== 'undefined') {
      window.removeEventListener('online', this.handleOnline.bind(this));
      window.removeEventListener('offline', this.handleOffline.bind(this));
    }
    
    this.removeAllListeners();
  }
}
