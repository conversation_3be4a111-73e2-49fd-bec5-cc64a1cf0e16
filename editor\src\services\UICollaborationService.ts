/**
 * UICollaborationService.ts
 * 
 * UI组件实时协作编辑服务
 */

import { EventEmitter } from '../utils/EventEmitter';
import { collaborationService as CollaborationService, Operation, OperationType } from './CollaborationService';
import { store } from '../store';
import { message } from 'antd';

/**
 * UI操作类型
 */
export enum UIOperationType {
  CREATE_COMPONENT = 'ui.create_component',
  UPDATE_COMPONENT = 'ui.update_component',
  DELETE_COMPONENT = 'ui.delete_component',
  MOVE_COMPONENT = 'ui.move_component',
  RESIZE_COMPONENT = 'ui.resize_component',
  STYLE_COMPONENT = 'ui.style_component',
  REORDER_COMPONENT = 'ui.reorder_component',
  GROUP_COMPONENTS = 'ui.group_components',
  UNGROUP_COMPONENTS = 'ui.ungroup_components',
  LOCK_COMPONENT = 'ui.lock_component',
  UNLOCK_COMPONENT = 'ui.unlock_component',
  HIDE_COMPONENT = 'ui.hide_component',
  SHOW_COMPONENT = 'ui.show_component'
}

/**
 * UI组件数据接口
 */
export interface UIComponentData {
  id: string;
  type: string;
  name: string;
  parentId?: string;
  properties: Record<string, any>;
  style: Record<string, any>;
  position: { x: number; y: number };
  size: { width: number; height: number };
  zIndex: number;
  visible: boolean;
  locked: boolean;
  children?: string[];
}

/**
 * 扩展的操作类型，包含UI操作
 */
type ExtendedOperationType = OperationType | UIOperationType;

/**
 * UI操作数据接口
 */
export interface UIOperationData {
  componentId: string;
  componentData?: UIComponentData;
  previousData?: UIComponentData;
  delta?: Partial<UIComponentData>;
  targetParentId?: string;
  targetIndex?: number;
  componentIds?: string[];
  groupId?: string;
}

/**
 * 用户光标位置接口
 */
export interface UserCursor {
  userId: string;
  userName: string;
  position: { x: number; y: number };
  componentId?: string;
  action?: string;
  timestamp: number;
}

/**
 * 用户选择状态接口
 */
export interface UserSelection {
  userId: string;
  userName: string;
  componentIds: string[];
  timestamp: number;
}

/**
 * UI协作编辑服务
 */
export class UICollaborationService extends EventEmitter {
  private static instance: UICollaborationService;
  private collaborationService: CollaborationService;
  private userCursors: Map<string, UserCursor> = new Map();
  private userSelections: Map<string, UserSelection> = new Map();
  private lockedComponents: Map<string, string> = new Map(); // componentId -> userId
  private isEnabled: boolean = false;
  private currentUserId: string = '';
  private _operationQueue: Operation[] = [];

  private constructor() {
    super();
    this.collaborationService = CollaborationService.getInstance();
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): UICollaborationService {
    if (!UICollaborationService.instance) {
      UICollaborationService.instance = new UICollaborationService();
    }
    return UICollaborationService.instance;
  }

  /**
   * 启用UI协作编辑
   */
  public enable(userId: string): void {
    this.isEnabled = true;
    this.currentUserId = userId;
    this.emit('enabled');
  }

  /**
   * 禁用UI协作编辑
   */
  public disable(): void {
    this.isEnabled = false;
    this.userCursors.clear();
    this.userSelections.clear();
    this.lockedComponents.clear();
    this.emit('disabled');
  }

  /**
   * 创建UI组件
   */
  public createComponent(componentData: UIComponentData): void {
    if (!this.isEnabled) return;

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.CREATE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId: componentData.id,
        componentData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentCreated', componentData);
  }

  /**
   * 更新UI组件
   */
  public updateComponent(componentId: string, updates: Partial<UIComponentData>, previousData?: UIComponentData): void {
    if (!this.isEnabled) return;

    // 检查组件是否被其他用户锁定
    if (this.isComponentLocked(componentId)) {
      const lockingUser = this.lockedComponents.get(componentId);
      message.warning(`组件正在被用户 ${lockingUser} 编辑中`);
      return;
    }

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.UPDATE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId,
        delta: updates,
        previousData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentUpdated', componentId, updates);
  }

  /**
   * 删除UI组件
   */
  public deleteComponent(componentId: string, componentData: UIComponentData): void {
    if (!this.isEnabled) return;

    // 检查组件是否被锁定
    if (this.isComponentLocked(componentId)) {
      const lockingUser = this.lockedComponents.get(componentId);
      message.warning(`组件正在被用户 ${lockingUser} 编辑中`);
      return;
    }

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.DELETE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId,
        componentData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentDeleted', componentId);
  }

  /**
   * 移动UI组件
   */
  public moveComponent(componentId: string, newPosition: { x: number; y: number }, previousPosition: { x: number; y: number }): void {
    if (!this.isEnabled) return;

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.MOVE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId,
        delta: { position: newPosition },
        previousData: { position: previousPosition } as UIComponentData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentMoved', componentId, newPosition);
  }

  /**
   * 调整UI组件大小
   */
  public resizeComponent(componentId: string, newSize: { width: number; height: number }, previousSize: { width: number; height: number }): void {
    if (!this.isEnabled) return;

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.RESIZE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId,
        delta: { size: newSize },
        previousData: { size: previousSize } as UIComponentData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentResized', componentId, newSize);
  }

  /**
   * 更新组件样式
   */
  public updateComponentStyle(componentId: string, styleUpdates: Record<string, any>, previousStyle: Record<string, any>): void {
    if (!this.isEnabled) return;

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.STYLE_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId,
        delta: { style: styleUpdates },
        previousData: { style: previousStyle } as UIComponentData
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentStyleUpdated', componentId, styleUpdates);
  }

  /**
   * 锁定组件
   */
  public lockComponent(componentId: string): void {
    if (!this.isEnabled) return;

    if (this.isComponentLocked(componentId)) {
      message.warning('组件已被锁定');
      return;
    }

    this.lockedComponents.set(componentId, this.currentUserId);

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.LOCK_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentLocked', componentId, this.currentUserId);
  }

  /**
   * 解锁组件
   */
  public unlockComponent(componentId: string): void {
    if (!this.isEnabled) return;

    const lockingUser = this.lockedComponents.get(componentId);
    if (lockingUser && lockingUser !== this.currentUserId) {
      message.warning('只有锁定用户可以解锁组件');
      return;
    }

    this.lockedComponents.delete(componentId);

    const operation: Operation = {
      id: this.generateId(),
      type: UIOperationType.UNLOCK_COMPONENT as ExtendedOperationType,
      userId: this.currentUserId,
      timestamp: Date.now(),
      data: {
        componentId
      } as UIOperationData
    };

    this.sendOperation(operation);
    this.emit('componentUnlocked', componentId);
  }

  /**
   * 更新用户光标位置
   */
  public updateCursor(position: { x: number; y: number }, componentId?: string, action?: string): void {
    if (!this.isEnabled) return;

    const cursor: UserCursor = {
      userId: this.currentUserId,
      userName: this.getCurrentUserName(),
      position,
      componentId,
      action,
      timestamp: Date.now()
    };

    // 发送光标更新消息
    this.collaborationService.sendMessage('cursor_update', cursor);
    this.emit('cursorUpdated', cursor);
  }

  /**
   * 更新用户选择状态
   */
  public updateSelection(componentIds: string[]): void {
    if (!this.isEnabled) return;

    const selection: UserSelection = {
      userId: this.currentUserId,
      userName: this.getCurrentUserName(),
      componentIds,
      timestamp: Date.now()
    };

    this.userSelections.set(this.currentUserId, selection);

    // 发送选择更新消息
    this.collaborationService.sendMessage('selection_update', selection);
    this.emit('selectionUpdated', selection);
  }

  /**
   * 获取用户光标
   */
  public getUserCursors(): UserCursor[] {
    return Array.from(this.userCursors.values()).filter(cursor => cursor.userId !== this.currentUserId);
  }

  /**
   * 获取用户选择
   */
  public getUserSelections(): UserSelection[] {
    return Array.from(this.userSelections.values()).filter(selection => selection.userId !== this.currentUserId);
  }

  /**
   * 检查组件是否被锁定
   */
  public isComponentLocked(componentId: string): boolean {
    const lockingUser = this.lockedComponents.get(componentId);
    return lockingUser !== undefined && lockingUser !== this.currentUserId;
  }

  /**
   * 获取锁定组件的用户
   */
  public getComponentLockingUser(componentId: string): string | undefined {
    return this.lockedComponents.get(componentId);
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听协作服务的操作事件
    this.collaborationService.on('operation', (operation: Operation) => {
      this.handleRemoteOperation(operation);
    });

    // 监听自定义消息
    this.collaborationService.on('message', (type: string, data: any) => {
      switch (type) {
        case 'cursor_update':
          this.handleCursorUpdate(data);
          break;
        case 'selection_update':
          this.handleSelectionUpdate(data);
          break;
      }
    });

    // 监听用户连接/断开
    this.collaborationService.on('userJoined', (user: any) => {
      this.emit('userJoined', user);
    });

    this.collaborationService.on('userLeft', (userId: string) => {
      this.userCursors.delete(userId);
      this.userSelections.delete(userId);
      
      // 解锁该用户锁定的组件
      for (const [componentId, lockingUserId] of this.lockedComponents.entries()) {
        if (lockingUserId === userId) {
          this.lockedComponents.delete(componentId);
          this.emit('componentUnlocked', componentId);
        }
      }
      
      this.emit('userLeft', userId);
    });
  }

  /**
   * 处理远程操作
   */
  private handleRemoteOperation(operation: Operation): void {
    if (operation.userId === this.currentUserId) return;

    const data = operation.data as UIOperationData;

    switch (operation.type) {
      case UIOperationType.CREATE_COMPONENT:
        this.emit('remoteComponentCreated', data.componentData);
        break;
      case UIOperationType.UPDATE_COMPONENT:
        this.emit('remoteComponentUpdated', data.componentId, data.delta);
        break;
      case UIOperationType.DELETE_COMPONENT:
        this.emit('remoteComponentDeleted', data.componentId);
        break;
      case UIOperationType.MOVE_COMPONENT:
        this.emit('remoteComponentMoved', data.componentId, data.delta?.position);
        break;
      case UIOperationType.RESIZE_COMPONENT:
        this.emit('remoteComponentResized', data.componentId, data.delta?.size);
        break;
      case UIOperationType.STYLE_COMPONENT:
        this.emit('remoteComponentStyleUpdated', data.componentId, data.delta?.style);
        break;
      case UIOperationType.LOCK_COMPONENT:
        this.lockedComponents.set(data.componentId, operation.userId);
        this.emit('remoteComponentLocked', data.componentId, operation.userId);
        break;
      case UIOperationType.UNLOCK_COMPONENT:
        this.lockedComponents.delete(data.componentId);
        this.emit('remoteComponentUnlocked', data.componentId);
        break;
    }
  }

  /**
   * 处理光标更新
   */
  private handleCursorUpdate(cursor: UserCursor): void {
    if (cursor.userId === this.currentUserId) return;

    this.userCursors.set(cursor.userId, cursor);
    this.emit('remoteCursorUpdated', cursor);
  }

  /**
   * 处理选择更新
   */
  private handleSelectionUpdate(selection: UserSelection): void {
    if (selection.userId === this.currentUserId) return;

    this.userSelections.set(selection.userId, selection);
    this.emit('remoteSelectionUpdated', selection);
  }

  /**
   * 发送操作
   */
  private sendOperation(operation: Operation): void {
    this.collaborationService.sendOperation(operation);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取当前用户名
   */
  private getCurrentUserName(): string {
    // 从store或其他地方获取当前用户名
    return store.getState().auth?.user?.name || 'Unknown User';
  }
}

// 导出单例实例
export const uiCollaborationService = UICollaborationService.getInstance();
